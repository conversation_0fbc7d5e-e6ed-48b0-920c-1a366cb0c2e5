"""
pytest 配置文件
"""
import pytest
from playwright.sync_api import Page, <PERSON><PERSON><PERSON>, BrowserContext


@pytest.fixture(scope="session")
def browser_context_args(browser_context_args):
    """浏览器上下文配置"""
    return {
        **browser_context_args,
        "viewport": {"width": 1920, "height": 1080},
        "ignore_https_errors": True,
    }


@pytest.fixture(scope="session")
def browser_type_launch_args(browser_type_launch_args):
    """浏览器启动参数配置 - 使用 Chrome"""
    return {
        **browser_type_launch_args,
        "channel": "chrome",  # 使用系统安装的 Chrome
        "headless": False,    # 显示浏览器窗口
        "slow_mo": 100,       # 操作间隔100ms，便于观察
    }


@pytest.fixture
def page_setup(page: Page):
    """页面设置"""
    # 设置默认超时时间
    page.set_default_timeout(30000)
    page.set_default_navigation_timeout(30000)
    
    yield page
    
    # 测试结束后清理
    try:
        page.close()
    except:
        pass


@pytest.fixture
def api_context(playwright):
    """API 测试上下文"""
    context = playwright.request.new_context(
        base_url="https://httpbin.org",
        extra_http_headers={
            "User-Agent": "Playwright Test Agent"
        }
    )
    yield context
    context.dispose()
