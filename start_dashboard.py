#!/usr/bin/env python3
"""
启动测试控制台
"""
import subprocess
import sys
import os


def check_dependencies():
    """检查依赖是否安装"""
    try:
        import streamlit
        print("✅ Streamlit 已安装")
        return True
    except ImportError:
        print("❌ Streamlit 未安装")
        return False


def install_dependencies():
    """安装依赖"""
    print("📦 正在安装依赖...")

    # 检测是否在 conda 环境中
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"🐍 检测到 conda 环境: {conda_env}")

    try:
        # 尝试使用 pip 安装
        cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]

        # 如果在中国，使用国内镜像源
        import locale
        if 'zh' in locale.getdefaultlocale()[0].lower():
            cmd.extend(["-i", "https://mirrors.aliyun.com/pypi/simple/"])

        subprocess.run(cmd, check=True)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        print("💡 请手动安装依赖:")
        print("   pip install -r requirements.txt")
        return False


def start_dashboard():
    """启动控制台"""
    print("🚀 启动测试控制台...")
    print("📍 访问地址: http://localhost:8501")
    print("🛑 按 Ctrl+C 停止服务")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            "test_dashboard.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--server.headless", "true"
        ])
    except KeyboardInterrupt:
        print("\n👋 测试控制台已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")


def main():
    """主函数"""
    print("🎭 Playwright 测试控制台启动器")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        if input("是否安装依赖? (y/n): ").lower() == 'y':
            if not install_dependencies():
                sys.exit(1)
        else:
            print("❌ 缺少依赖，无法启动")
            sys.exit(1)
    
    # 确保报告目录存在
    os.makedirs("reports", exist_ok=True)
    
    # 启动控制台
    start_dashboard()


if __name__ == "__main__":
    main()
